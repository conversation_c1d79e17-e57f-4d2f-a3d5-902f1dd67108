import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>ield,
  Chip,
  Card,
  CardContent,
  IconButton,
  Button,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Modal,
  Snackbar
} from '@mui/material';
import {
  Search as SearchIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  ContentCopy as CopyIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import LabelIcon from '@mui/icons-material/Label';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import StarIcon from '@mui/icons-material/Star';
import { getSavedResponsesProvider, type UnifiedSavedResponse } from '../../utils/saveResponseProvider';

interface SavedResponsesProps {
  onClose?: () => void;
}

export default function SavedResponses({ onClose }: SavedResponsesProps) {
  const [items, setItems] = useState<UnifiedSavedResponse[]>([]);
  const [filteredItems, setFilteredItems] = useState<UnifiedSavedResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTag, setSelectedTag] = useState<string>('');
  const [selectedSource, setSelectedSource] = useState<string>('');
  const [allTags, setAllTags] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<UnifiedSavedResponse | null>(null);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Load on component mount
  useEffect(() => {
    loadItems();
  }, []);

  // Filter when search, tag, or source changes
  useEffect(() => {
    filterItems();
  }, [items, searchQuery, selectedTag, selectedSource]);

  const loadItems = async () => {
    try {
      setLoading(true);
      const provider = await getSavedResponsesProvider();
      const result = await provider.getResponses({
        // initial load, no filters; client-side filters will apply below
        page_size: 50,
      });
      setItems(result.results);
      // Build tag list from results
      const tagSet = new Set<string>();
      result.results.forEach(r => (r.tags || []).forEach(t => tagSet.add(t)));
      setAllTags(Array.from(tagSet).sort());
    } catch (err) {
      setError('Failed to load saved responses');
      console.error('Error loading saved responses:', err);
    } finally {
      setLoading(false);
    }
  };

  const filterItems = async () => {
    let filtered = [...items];

    // Apply search filter
    if (searchQuery.trim()) {
      const q = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        (item.title || '').toLowerCase().includes(q) ||
        (item.response || '').toLowerCase().includes(q) ||
        (item.prompt || '').toLowerCase().includes(q) ||
        (item.note || '').toLowerCase().includes(q) ||
        (item.tags || []).some(t => t.toLowerCase().includes(q))
      );
    }

    // Apply tag filter
    if (selectedTag) {
      filtered = filtered.filter(item => 
        (item.tags || []).includes(selectedTag)
      );
    }

    // Apply source filter
    if (selectedSource) {
      filtered = filtered.filter(item => 
        (item.llm_source || '') === selectedSource
      );
    }

    setFilteredItems(filtered);
  };

  const handleDelete = async (id: string) => {
    try {
      const provider = await getSavedResponsesProvider();
      await provider.deleteResponse(id);
      setItems(prev => prev.filter(b => b.id !== id));
      setSnackbar({ open: true, message: 'Deleted successfully', severity: 'success' });
    } catch (err) {
      setError('Failed to delete');
      console.error('Error deleting saved response:', err);
    }
  };

  const handleCopyContent = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setSnackbar({ open: true, message: 'Content copied to clipboard!', severity: 'success' });
    } catch (err) {
      console.error('Error copying content:', err);
      setSnackbar({ open: true, message: 'Failed to copy content', severity: 'error' });
    }
  };

  const handleViewItem = (item: UnifiedSavedResponse) => {
    setSelectedItem(item);
    setViewModalOpen(true);
  };

  const handleCloseViewModal = () => {
    setViewModalOpen(false);
    setSelectedItem(null);
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedTag('');
    setSelectedSource('');
  };

  const getUniqueSources = () => {
    return [...new Set(items.map(b => b.llm_source))].filter(Boolean).sort();
  };

  const formatDateIso = (iso: string) => {
    return new Date(iso).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
      <Box sx={{ p: 0, display: 'flex', flexDirection: 'column', height: '100%', minHeight: 0 }}>
        {/* Header: Search & Filters */}
        <Box sx={{ p: 2, pb: 1, borderBottom: '1px solid #e0e0e0', background: '#fff', zIndex: 1 }}>
          <Typography fontWeight={600} fontSize={15} sx={{ mb: 1 }}>🔍 Search & Filters</Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
          {/* Search Bar */}
          <TextField
            fullWidth
            size="small"
              placeholder="Search saved responses..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <span role="img" aria-label="search">🔎</span>,
              endAdornment: searchQuery && (
                <IconButton size="small" onClick={() => setSearchQuery('')}>
                  <ClearIcon />
                </IconButton>
              ),
              style: { borderRadius: 8 }
            }}
            sx={{ mb: 0, flex: 2, minWidth: 180 }}
          />
            {/* Tag Filter */}
          <FormControl size="small" sx={{ minWidth: 120, flex: 1 }}>
            <InputLabel>🏷️ Tag</InputLabel>
            <Select
              value={selectedTag}
              label="🏷️ Tag"
              onChange={(e) => setSelectedTag(e.target.value)}
            >
              <MenuItem value="">All Tags</MenuItem>
              {allTags.map(tag => (
                <MenuItem key={tag} value={tag}>
                  #{tag}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
            {/* LLM Source Filter */}
          <FormControl size="small" sx={{ minWidth: 120, flex: 1 }}>
              <InputLabel>🤖 LLM</InputLabel>
            <Select
              value={selectedSource}
                label="🤖 LLM"
              onChange={(e) => setSelectedSource(e.target.value)}
            >
                <MenuItem value="">All LLMs</MenuItem>
              {getUniqueSources().map(source => (
                <MenuItem key={source} value={source}>
                  {source}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* Clear Button */}
          <Button
            variant="outlined"
            size="small"
            startIcon={<FilterIcon />}
            onClick={clearFilters}
            sx={{ minWidth: 80 }}
          >
            Clear
          </Button>
        </Box>
      </Box>
        <Divider sx={{ mb: 0 }} />

        {/* Saved Responses List */}
        <Box sx={{ flex: 1, overflowY: 'auto', background: '#f9fafb', p: 2, display: 'flex', flexDirection: 'column', gap: 2, minHeight: 0 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
            <CircularProgress />
            <Typography sx={{ ml: 2 }}>Loading saved responses...</Typography>
          </Box>
        ) : filteredItems.length === 0 ? (
          <Box textAlign="center" py={4}>
            <Typography color="text.secondary">
              {items.length === 0 
                ? "No saved responses yet. Bookmark responses locally or log in to sync."
                : "No saved responses match your search."
              }
            </Typography>
          </Box>
        ) : (
          <>
            {filteredItems.map((item) => (
              <Card key={item.id} sx={{ 
                borderRadius: 2,
                border: '1px solid #e0e0e0',
                boxShadow: 0,
                position: 'relative',
                overflow: 'visible'
              }}>
                {item.is_favorite && (
                  <Box sx={{ position: 'absolute', top: 8, right: 8, bgcolor: '#fef08a', color: '#b45309', px: 1, py: 0.5, borderRadius: 1, fontSize: 11, fontWeight: 700, zIndex: 1 }}>
                    <StarIcon fontSize="inherit" sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} /> FAVORITE
                  </Box>
                )}
                <CardContent sx={{ p: 2, pb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                    <Typography variant="subtitle1" fontWeight={600} sx={{ color: '#1e293b', flex: 1 }}>
                      {item.title || 'Untitled'}
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ mt: 1, mb: 1, color: '#374151', whiteSpace: 'pre-line' }}>
                    {item.response.length > 200 
                      ? `${item.response.substring(0, 200)}...` 
                      : item.response
                    }
                  </Typography>

                  {/* Tags & LLM */}
                  <Box sx={{ mb: 1, display: 'flex', flexWrap: 'wrap', gap: 0.75, alignItems: 'center' }}>
                    {item.tags && item.tags.map(tag => (
                      <Chip
                        key={tag}
                        icon={<LabelIcon sx={{ fontSize: 16 }} />}
                        label={tag}
                        size="small"
                        sx={{ bgcolor: '#f3e8ff', color: '#7c3aed', fontWeight: 500, '& .MuiChip-icon': { color: '#a855f7' } }}
                      />
                    ))}
                    {item.llm_source && (
                      <Chip
                        icon={<SmartToyIcon sx={{ fontSize: 16 }} />}
                        label={item.llm_source}
                        size="small"
                        sx={{ bgcolor: '#dbeafe', color: '#1e40af', fontWeight: 500, '& .MuiChip-icon': { color: '#3b82f6' } }}
                      />
                    )}
                  </Box>

                  {/* Note */}
                  {item.note && (
                    <Typography 
                      variant="caption" 
                      sx={{ color: '#64748b', fontStyle: 'italic' }}
                    >
                      {item.note}
                    </Typography>
                  )}

                  <Divider sx={{ my: 1 }} />
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, fontSize: 12, color: '#64748b' }}>
                    <span>📅 {formatDateIso(item.created_at)}</span>
                    <span style={{ marginLeft: 'auto', color: '#2563eb', fontWeight: 500 }}>Source: {item.source === 'api' ? 'Cloud' : 'Local'}</span>
                  </Box>
                  <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'nowrap', justifyContent: 'flex-end', mt: 1 }}>
                    <Tooltip title="Copy">
                      <IconButton size="small" onClick={() => handleCopyContent(item.response)}>
                        <CopyIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="View Details">
                      <IconButton size="small" onClick={() => handleViewItem(item)}>
                        <ViewIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete">
                      <IconButton size="small" onClick={() => handleDelete(item.id)} sx={{ color: 'error.main' }}>
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </>
        )}
      </Box>

      {/* View Modal */}
      <Modal open={viewModalOpen} onClose={handleCloseViewModal}>
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '90%',
          maxWidth: 800,
          maxHeight: '90vh',
          bgcolor: 'background.paper',
          borderRadius: 2,
          boxShadow: 24,
          p: 3,
          display: 'flex',
          flexDirection: 'column'
        }}>
          {selectedItem && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h5" fontWeight={600} sx={{ color: '#1e293b' }}>
                  {selectedItem.title || 'Untitled'}
                </Typography>
                <Box>
                  <Tooltip title="Copy content">
                    <IconButton 
                      size="small" 
                      onClick={() => handleCopyContent(selectedItem.response)}
                    >
                      <CopyIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <IconButton size="small" onClick={handleCloseViewModal}>
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>

              {/* Tags */}
              {(selectedItem.tags && selectedItem.tags.length > 0) && (
                <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selectedItem.tags.map(tag => (
                    <Chip
                      key={tag}
                      label={`#${tag}`}
                      size="small"
                      sx={{ background: '#e0e0e0', color: '#374151' }}
                    />
                  ))}
                </Box>
              )}

              {/* Note */}
              {selectedItem.note && (
                <Box sx={{ mb: 2, p: 2, bgcolor: '#f8f9fa', borderRadius: 1, borderLeft: '4px solid #4a90e2' }}>
                  <Typography variant="body2" sx={{ color: '#64748b', fontStyle: 'italic' }}>
                    <strong>Note:</strong> {selectedItem.note}
                  </Typography>
                </Box>
              )}

              {/* Content */}
              <Box sx={{ 
                flex: 1, 
                overflow: 'auto', 
                bgcolor: '#f8f9fa', 
                p: 2, 
                borderRadius: 1,
                border: '1px solid #e0e0e0'
              }}>
                <Typography variant="body1" sx={{ 
                  color: '#374151', 
                  lineHeight: 1.6,
                  whiteSpace: 'pre-wrap',
                  fontFamily: 'monospace'
                }}>
                  {selectedItem.response}
                </Typography>
              </Box>

              {/* Metadata */}
              <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #e0e0e0' }}>
                <Typography variant="caption" color="text.secondary">
                  {selectedItem.llm_source} • {formatDateIso(selectedItem.created_at)}
                </Typography>
              </Box>
            </>
          )}
        </Box>
      </Modal>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
} 