/// <reference types="chrome"/>

import {
  handleGoogleSignIn,
  saveAuthState,
  clearAuthState,
} from "@/utils/auth";
import { promptCircle<PERSON>pi } from "@/utils/promptCircleApi";

export default defineBackground({
  main() {
    // Handle messages from popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {

      if (message.type === "START_GOOGLE_LOGIN") {
        handleGoogleLogin()
          .then((result) => {
            // Send success response back to popup using sendResponse
            sendResponse({
              type: "GOOGLE_LOGIN_RESULT",
              success: true,
              authState: result,
            });
          })
          .catch((error) => {
            // Send error response back to popup using sendResponse
            sendResponse({
              type: "GOOGLE_LOGIN_RESULT",
              success: false,
              error: error.message,
            });
          });
        return true; // Keep the message channel open for async response
      }

      // Handle PromptCircle API calls
      if (message.type === "PROMPTCIRCLE_CHECK_AUTH") {
        promptCircleApi
          .checkAuth()
          .then((result) => {
            sendResponse({
              type: "PROMPTCIRCLE_AUTH_RESULT",
              success: true,
              isAuthenticated: result.isAuthenticated,
            });
          })
          .catch((error) => {
            sendResponse({
              type: "PROMPTCIRCLE_AUTH_RESULT",
              success: false,
              error: error.message,
            });
          });
        return true;
      }

      if (message.type === "PROMPTCIRCLE_OPTIMIZE_PROMPT") {
        promptCircleApi
          .optimizePrompt(message.prompt)
          .then((result) => {
            sendResponse({
              type: "PROMPTCIRCLE_OPTIMIZE_RESULT",
              success: true,
              data: result,
            });
          })
          .catch((error) => {
            sendResponse({
              type: "PROMPTCIRCLE_OPTIMIZE_RESULT",
              success: false,
              error: error.message,
            });
          });
        return true;
      }

      if (message.type === "PROMPTCIRCLE_LOGOUT") {
        promptCircleApi
          .logout()
          .then(() => {
            sendResponse({
              type: "PROMPTCIRCLE_LOGOUT_RESULT",
              success: true,
            });
          })
          .catch((error) => {
            sendResponse({
              type: "PROMPTCIRCLE_LOGOUT_RESULT",
              success: false,
              error: error.message,
            });
          });
        return true;
      }

      if (message.type === "PROMPTCIRCLE_OPEN_LOGIN") {
        promptCircleApi.openLoginPage();
        sendResponse({ success: true });
        return false;
      }

      if (message.type === "PROMPTCIRCLE_GET_SESSION_INFO") {
        promptCircleApi
          .getSessionInfo()
          .then((sessionInfo) => {
            if (sessionInfo) {
              sendResponse({
                type: "PROMPTCIRCLE_SESSION_INFO_RESULT",
                success: true,
                sessionInfo,
              });
            } else {
              sendResponse({
                type: "PROMPTCIRCLE_SESSION_INFO_RESULT",
                success: false,
                error:
                  "No session info available - user may not be authenticated",
              });
            }
          })
          .catch((error) => {
            sendResponse({
              type: "PROMPTCIRCLE_SESSION_INFO_RESULT",
              success: false,
              error: error.message,
            });
          });
        return true;
      }

      if (message.type === "PROMPTCIRCLE_GET_SAVED_PROMPTS") {
        promptCircleApi
          .getSavedPrompts(message.params)
          .then((result) => {
            if ('error' in result) {
              sendResponse({
                type: "PROMPTCIRCLE_SAVED_PROMPTS_RESULT",
                success: false,
                error: result.error,
              });
            } else {
              sendResponse({
                type: "PROMPTCIRCLE_SAVED_PROMPTS_RESULT",
                success: true,
                data: result,
              });
            }
          })
          .catch((error) => {
            sendResponse({
              type: "PROMPTCIRCLE_SAVED_PROMPTS_RESULT",
              success: false,
              error: error.message,
            });
          });
        return true;
      }

      if (message.action === "openPopup") {
        // @ts-ignore
        if (chrome.action && chrome.action.openPopup) {
          // @ts-ignore
          chrome.action.openPopup();
        } else {
          console.warn("chrome.action.openPopup() is not available.");
        }
        return false;
      }

      // Handle fillPrompt action - forward to content script
      if (message.action === "fillPrompt" && message.tabId) {
        chrome.tabs.sendMessage(
          message.tabId,
          {
            action: "fillPrompt",
            prompt: message.prompt,
            selector: message.selector,
          },
          (response) => {
            if (chrome.runtime.lastError) {
              sendResponse({ error: chrome.runtime.lastError.message });
            } else {
              sendResponse(response);
            }
          }
        );
        return true; // Keep the message channel open for async response
      }

      // Handle openAndFillPrompt action
      if (message.action === "openAndFillPrompt") {
        const { url, prompt, selector } = message;
        
        // Check if Chrome API and scripting API are available
        if (!chrome || !chrome.scripting) {
          sendResponse({ error: "Chrome API or Scripting API not available. Please check extension permissions." });
          return true;
        }
        
        chrome.tabs.create({ url }, async (tab) => {
          if (!tab.id) {
            sendResponse({ error: "Failed to create tab." });
            return;
          }
          
          try {
            // Inject the content script programmatically
            await chrome.scripting.executeScript({
              target: { tabId: tab.id },
              files: ["content-scripts/content.js"],
            });
            
            // Wait for handshake from content script
            const waitForContentScriptReady = (
              tabId: number,
              timeout = 10000
            ) => {
              return new Promise((resolve, reject) => {
                let timer: ReturnType<typeof setTimeout>;
                function onMessage(msg: any, sender: any) {
                  if (
                    msg.action === "contentScriptReady" &&
                    sender.tab &&
                    sender.tab.id === tabId
                  ) {
                    chrome.runtime.onMessage.removeListener(onMessage);
                    clearTimeout(timer);
                    resolve(undefined);
                  }
                }
                chrome.runtime.onMessage.addListener(onMessage);
                timer = setTimeout(() => {
                  chrome.runtime.onMessage.removeListener(onMessage);
                  reject(new Error("Content script not ready in time"));
                }, timeout);
              });
            };
            
            await waitForContentScriptReady(tab.id!);
            
            // Send message to content script and handle response properly
            chrome.tabs.sendMessage(
              tab.id!,
              {
                action: "fillPrompt",
                prompt,
                selector,
              },
              (response) => {
                if (chrome.runtime.lastError) {
                  sendResponse({ error: chrome.runtime.lastError.message });
                } else {
                  sendResponse({ success: true, response });
                }
              }
            );
          } catch (err) {
            sendResponse({ 
              error: err instanceof Error ? err.message : "Failed to execute script or fill prompt." 
            });
          }
        });
        return true; // Keep the message channel open for async response
      }
    });

    // Handle extension installation or update
    // @ts-ignore
    chrome.runtime.onInstalled.addListener(() => {
      // Clear any existing auth state
      clearAuthState();
    });
  },
});

// Handle Google login process
async function handleGoogleLogin() {
  try {
    const authState = await handleGoogleSignIn();

    // Broadcast auth state change to all extension contexts with error handling
    try {
      chrome.runtime.sendMessage({
        type: "AUTH_STATE_CHANGED",
        authState,
      });
    } catch (error) {
      // Ignore errors when no receivers are available
    }

    // Send message to all tab content scripts with error handling
    // @ts-ignore
    if (chrome.tabs && chrome.tabs.query) {
      try {
        // @ts-ignore
        const tabs = await chrome.tabs.query({});
        for (const tab of tabs) {
          if (tab.id) {
            chrome.tabs.sendMessage(tab.id, {
              type: "AUTH_STATE_CHANGED",
              authState,
            });
          }
        }
      } catch (error) {
        // Ignore errors when tabs are not available
      }
    }

    return authState;
  } catch (error) {
    throw error;
  }
}
