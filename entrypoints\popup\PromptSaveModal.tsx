import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, Modal, Chip, FormControl, InputLabel, Select, MenuItem, Switch, FormControlLabel } from '@mui/material';

export interface PromptData {
  title: string;
  content: string;
  tags: string[];
  folder: string;
  compatible_llms: string[];
  is_favorite: boolean;
  optimize?: string;
}

interface PromptSaveModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: PromptData) => void;
  initialPrompt?: Partial<PromptData>;
  allFolders: string[];
  loading?: boolean;
  mode?: 'add' | 'edit';
}

export default function PromptSaveModal({ open, onClose, onSave, initialPrompt = {}, allFolders, loading, mode = 'add' }: PromptSaveModalProps) {
  const [title, setTitle] = useState(initialPrompt.title || '');
  const [content, setContent] = useState(initialPrompt.content || '');
  const [tags, setTags] = useState<string[]>(initialPrompt.tags || []);
  const [folder, setFolder] = useState(initialPrompt.folder || '');
  const [compatible_llms, setCompatibleLlms] = useState<string[]>(initialPrompt.compatible_llms || ['ChatGPT']);
  const [is_favorite, setIsFavorite] = useState(initialPrompt.is_favorite || false);
  const [optimize, setOptimize] = useState(initialPrompt.optimize || '');
  const [tagInput, setTagInput] = useState('');
  const [folderInput, setFolderInput] = useState('');

  const availableLLMs = ['ChatGPT', 'Claude', 'Gemini'];

  useEffect(() => {
    setTitle(initialPrompt.title || '');
    setContent(initialPrompt.content || '');
    setTags(initialPrompt.tags || []);
    setFolder(initialPrompt.folder || '');
    setCompatibleLlms(initialPrompt.compatible_llms || ['ChatGPT']);
    setIsFavorite(initialPrompt.is_favorite || false);
    setOptimize(initialPrompt.optimize || '');
  }, [initialPrompt, open]);

  const handleAddTag = () => {
    const tag = tagInput.trim();
    if (tag && !tags.includes(tag)) {
      setTags([...tags, tag]);
    }
    setTagInput('');
  };
  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };
  const handleSelectFolder = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFolder(e.target.value);
    setFolderInput('');
  };
  const handleFolderInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFolderInput(e.target.value);
    setFolder(e.target.value);
  };
  const handleCompatibleLlmsChange = (event: any) => {
    const value = event.target.value;
    setCompatibleLlms(typeof value === 'string' ? value.split(',') : value);
  };
  const handleSave = () => {
    if (!title.trim() || !content.trim()) return;
    onSave({ title, content, tags, folder, compatible_llms, is_favorite, optimize });
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box sx={{ p: 3, background: '#fff', borderRadius: 2, boxShadow: 3, minWidth: 350, maxWidth: 400, mx: 'auto', mt: 8, display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography variant="h6">{mode === 'edit' ? 'Edit Prompt' : 'Add New Prompt'}</Typography>
        <input
          value={title}
          onChange={e => setTitle(e.target.value)}
          placeholder="Title"
          style={{ padding: 6, borderRadius: 6, border: '1px solid #e0e0e0', fontSize: 14 }}
        />
        <textarea
          value={content}
          onChange={e => setContent(e.target.value)}
          placeholder="Prompt content"
          style={{ padding: 6, borderRadius: 6, border: '1px solid #e0e0e0', fontSize: 14, minHeight: 60 }}
        />
        {/* Tags input */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <input
              value={tagInput}
              onChange={e => setTagInput(e.target.value)}
              placeholder="Add tag"
              style={{ flex: 1, padding: 6, borderRadius: 6, border: '1px solid #e0e0e0', fontSize: 14 }}
              onKeyDown={e => { if (e.key === 'Enter') { e.preventDefault(); handleAddTag(); } }}
            />
            <Button size="small" onClick={handleAddTag}>Add</Button>
          </Box>
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
            {tags.map(tag => (
              <Chip key={tag} label={tag} onDelete={() => handleRemoveTag(tag)} size="small" sx={{ background: '#e0e0e0' }} />
            ))}
          </Box>
        </Box>
        {/* Folder input */}
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <select
            value={allFolders.includes(folder) ? folder : ''}
            onChange={handleSelectFolder}
            style={{ padding: 6, borderRadius: 6, border: '1px solid #e0e0e0', fontSize: 14 }}
          >
            <option value="">Select folder</option>
            {allFolders.map(f => (
              <option key={f} value={f}>{f}</option>
            ))}
          </select>
          <input
            value={folderInput}
            onChange={handleFolderInput}
            placeholder="Or create new folder"
            style={{ flex: 1, padding: 6, borderRadius: 6, border: '1px solid #e0e0e0', fontSize: 14 }}
          />
        </Box>
        {/* Compatible LLMs selectbox */}
        <FormControl fullWidth size="small">
          <InputLabel>Compatible LLMs</InputLabel>
          <Select
            multiple
            value={compatible_llms}
            onChange={handleCompatibleLlmsChange}
            label="Compatible LLMs"
            renderValue={(selected) => (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {selected.map((value) => (
                  <Chip key={value} label={value} size="small" />
                ))}
              </Box>
            )}
          >
            {availableLLMs.map((llm) => (
              <MenuItem key={llm} value={llm}>
                {llm}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {/* Favorite toggle switch */}
        <FormControlLabel
          control={
            <Switch
              checked={is_favorite}
              onChange={(e) => setIsFavorite(e.target.checked)}
              color="primary"
            />
          }
          label="Mark as favorite"
        />
        {/* Optimize field (optional) */}
        {/* <input
          value={optimize}
          onChange={e => setOptimize(e.target.value)}
          placeholder="Optimize (optional)"
          style={{ padding: 6, borderRadius: 6, border: '1px solid #e0e0e0', fontSize: 14 }}
        /> */}
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
          <Button onClick={onClose}>Cancel</Button>
          <Button variant="contained" onClick={handleSave} disabled={loading}>{mode === 'edit' ? 'Update' : 'Add'}</Button>
        </Box>
      </Box>
    </Modal>
  );
} 