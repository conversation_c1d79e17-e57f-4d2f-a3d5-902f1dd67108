import { SavedPrompt, SavedPromptsResponse, ApiError } from './promptCircleApi';
import { getAuthState } from './auth';
import type { AuthState } from '../types/auth';

// Unified interface for both local and API prompts
export interface UnifiedPrompt {
  id: string;
  title: string;
  content: string;
  tags: string[];
  folder: string;
  compatible_llms: string[];
  isFavorite: boolean;
  usage_count?: number;
  created_at: string;
  updated_at: string;
  source: 'local' | 'api';
}

// Convert API prompt to unified format
export function convertApiPromptToUnified(apiPrompt: SavedPrompt): UnifiedPrompt {
  try {
    console.log('Converting API prompt:', apiPrompt);
    
    const unifiedPrompt = {
      id: (apiPrompt.id?.toString()) || `api_${Date.now()}_${Math.random()}`,
      title: apiPrompt.title || 'Untitled Prompt',
      content: apiPrompt.content || '',
      tags: apiPrompt.tags || [],
      folder: apiPrompt.folder || '',
      compatible_llms: apiPrompt.compatible_llms || [],
      isFavorite: apiPrompt.is_favorite || false,
      usage_count: apiPrompt.usage_count || 0,
      created_at: apiPrompt.created_at || new Date().toISOString(),
      updated_at: apiPrompt.updated_at || new Date().toISOString(),
      source: 'api' as const
    };
    
    console.log('Converted to unified prompt:', unifiedPrompt);
    return unifiedPrompt;
  } catch (error) {
    console.error('Error converting API prompt:', error, apiPrompt);
    // Return a fallback prompt if conversion fails
    return {
      id: `api_fallback_${Date.now()}`,
      title: 'Error Loading Prompt',
      content: 'Failed to load prompt content',
      tags: [],
      folder: '',
      compatible_llms: [],
      isFavorite: false,
      usage_count: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      source: 'api' as const
    };
  }
}

// Convert local prompt to unified format
export function convertLocalPromptToUnified(localPrompt: any): UnifiedPrompt {
  return {
    id: localPrompt.id,
    title: localPrompt.title,
    content: localPrompt.content,
    tags: localPrompt.tags || [],
    folder: localPrompt.folder || '',
    compatible_llms: [localPrompt.compatible_llm || 'ChatGPT'],
    isFavorite: localPrompt.isFavorite || false,
    created_at: new Date(localPrompt.createdAt).toISOString(),
    updated_at: new Date(localPrompt.updatedAt).toISOString(),
    source: 'local'
  };
}

// Data provider interface
export interface LibraryDataProvider {
  getPrompts(params?: any): Promise<{ prompts: UnifiedPrompt[]; pagination?: any }>;
  savePrompt(prompt: any): Promise<void>;
  updatePrompt(id: string, prompt: any): Promise<void>;
  deletePrompt(id: string): Promise<void>;
  toggleFavorite(id: string): Promise<void>;
  usePrompt(id: string): Promise<{ usage_count: number; content: string } | { error: string }>;
}

// Local storage provider (existing functionality)
export class LocalStorageProvider implements LibraryDataProvider {
  async getPrompts(params?: any): Promise<{ prompts: UnifiedPrompt[]; pagination?: any }> {
    return new Promise((resolve) => {
      chrome.storage.local.get(['promptLibrary'], (result) => {
        const library = Array.isArray(result.promptLibrary) ? result.promptLibrary : [];
        const unifiedPrompts = library.map(convertLocalPromptToUnified);
        resolve({ prompts: unifiedPrompts });
      });
    });
  }

  async savePrompt(prompt: any): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.local.get(['promptLibrary'], (result) => {
        const library = Array.isArray(result.promptLibrary) ? result.promptLibrary : [];
        const newPrompt = {
          id: Date.now().toString(),
          ...prompt,
          isFavorite: false,
          createdAt: Date.now(),
          updatedAt: Date.now()
        };
        const updated = [newPrompt, ...library];
        chrome.storage.local.set({ promptLibrary: updated }, resolve);
      });
    });
  }

  async updatePrompt(id: string, prompt: any): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.local.get(['promptLibrary'], (result) => {
        const library = Array.isArray(result.promptLibrary) ? result.promptLibrary : [];
        const updated = library.map(item =>
          item.id === id
            ? { ...item, ...prompt, updatedAt: Date.now() }
            : item
        );
        chrome.storage.local.set({ promptLibrary: updated }, resolve);
      });
    });
  }

  async deletePrompt(id: string): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.local.get(['promptLibrary'], (result) => {
        const library = Array.isArray(result.promptLibrary) ? result.promptLibrary : [];
        const updated = library.filter(item => item.id !== id);
        chrome.storage.local.set({ promptLibrary: updated }, resolve);
      });
    });
  }

  async toggleFavorite(id: string): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.local.get(['promptLibrary'], (result) => {
        const library = Array.isArray(result.promptLibrary) ? result.promptLibrary : [];
        const updated = library.map(item =>
          item.id === id ? { ...item, isFavorite: !item.isFavorite, updatedAt: Date.now() } : item
        );
        chrome.storage.local.set({ promptLibrary: updated }, resolve);
      });
    });
  }

  async usePrompt(id: string): Promise<{ usage_count: number; content: string } | { error: string }> {
    return new Promise((resolve) => {
      chrome.storage.local.get(['promptLibrary'], (result) => {
        const library = Array.isArray(result.promptLibrary) ? result.promptLibrary : [];
        const promptIndex = library.findIndex(item => item.id === id);

        if (promptIndex === -1) {
          resolve({ error: 'Prompt not found' });
          return;
        }

        // Increment usage count
        const updatedLibrary = [...library];
        const currentUsageCount = updatedLibrary[promptIndex].usage_count || 0;
        updatedLibrary[promptIndex] = {
          ...updatedLibrary[promptIndex],
          usage_count: currentUsageCount + 1,
          updatedAt: Date.now()
        };

        // Save updated library
        chrome.storage.local.set({ promptLibrary: updatedLibrary }, () => {
          resolve({ 
            usage_count: updatedLibrary[promptIndex].usage_count, 
            content: updatedLibrary[promptIndex].content 
          });
        });
      });
    });
  }
}

// API provider (new functionality)
export class PromptCircleApiProvider implements LibraryDataProvider {
  async getPrompts(params?: any): Promise<{ prompts: UnifiedPrompt[]; pagination?: any }> {
    try {
      const { promptCircleApi } = await import('./promptCircleApi');
      
      const apiParams = {
        compatible_llm: params?.compatible_llm,
        favorite: params?.favorite,
        folder: params?.folder,
        page: params?.page,
        page_size: params?.page_size || 10,
        search: params?.search,
        sort: params?.sort
      };

      const result = await promptCircleApi.getSavedPrompts(apiParams);
      
      if ('error' in result) {
        console.error('API returned error:', result.error);
        throw new Error(result.error);
      }

      // Ensure result.results is an array and handle potential null/undefined values
      if (!result.results || !Array.isArray(result.results)) {
        return {
          prompts: [],
          pagination: {
            count: 0,
            next: null,
            previous: null,
            currentPage: params?.page || 1
          }
        };
      }

      const unifiedPrompts = result.results
        .filter(item => item && typeof item === 'object') // Filter out null/undefined items
        .map(convertApiPromptToUnified);
            
      return {
        prompts: unifiedPrompts,
        pagination: {
          count: result.count,
          next: result.next,
          previous: result.previous,
          currentPage: params?.page || 1
        }
      };
    } catch (error) {
      console.error('Error fetching prompts from API:', error);
      // Return empty result instead of throwing to prevent UI crashes
      return {
        prompts: [],
        pagination: {
          count: 0,
          next: null,
          previous: null,
          currentPage: params?.page || 1
        }
      };
    }
  }

  async savePrompt(prompt: any): Promise<void> {
    try {
      const { promptCircleApi } = await import('./promptCircleApi');
      
      // Transform the prompt data to match API format
      const apiPromptData = {
        title: prompt.title,
        content: prompt.content,
        tags: prompt.tags || [],
        folder: prompt.folder || '',
        compatible_llms: prompt.compatible_llms || [prompt.compatible_llm || 'ChatGPT'],
        is_favorite: prompt.isFavorite || false
      };

      const result = await promptCircleApi.savePrompt(apiPromptData);
      
      if ('error' in result) {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error saving prompt to API:', error);
      throw error;
    }
  }

  async updatePrompt(id: string, prompt: any): Promise<void> {
    try {
      const { promptCircleApi } = await import('./promptCircleApi');
      
      // Transform the prompt data to match API format
      const apiPromptData: any = {};
      if (prompt.title !== undefined) apiPromptData.title = prompt.title;
      if (prompt.content !== undefined) apiPromptData.content = prompt.content;
      if (prompt.tags !== undefined) apiPromptData.tags = prompt.tags;
      if (prompt.folder !== undefined) apiPromptData.folder = prompt.folder;
      if (prompt.compatible_llms !== undefined) apiPromptData.compatible_llms = prompt.compatible_llms;
      if (prompt.isFavorite !== undefined) apiPromptData.is_favorite = prompt.isFavorite;

      const result = await promptCircleApi.updatePrompt(id, apiPromptData);
      
      if ('error' in result) {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error updating prompt in API:', error);
      throw error;
    }
  }

  async deletePrompt(id: string): Promise<void> {
    try {
      const { promptCircleApi } = await import('./promptCircleApi');
      
      const result = await promptCircleApi.deletePrompt(id);
      
      if ('error' in result) {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error deleting prompt from API:', error);
      throw error;
    }
  }

  async toggleFavorite(id: string): Promise<void> {
    try {
      const { promptCircleApi } = await import('./promptCircleApi');
      
      // Get the current prompt to find its current favorite status
      const promptsResult = await promptCircleApi.getSavedPrompts();
      
      if ('error' in promptsResult) {
        throw new Error(promptsResult.error);
      }
      
      // Find the prompt with the given ID
      const prompt = promptsResult.results.find(p => p.id.toString() === id);
      
      if (!prompt) {
        throw new Error('Prompt not found');
      }
      
      // Toggle the favorite status
      const newFavoriteStatus = !prompt.is_favorite;
      
      // Use updatePrompt to toggle the favorite status
      const result = await promptCircleApi.updatePrompt(id, {
        is_favorite: newFavoriteStatus
      });
      
      if ('error' in result) {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error toggling favorite in API:', error);
      throw error;
    }
  }

  async usePrompt(id: string): Promise<{ usage_count: number; content: string } | { error: string }> {
    try {
      const { promptCircleApi } = await import('./promptCircleApi');
      
      const result = await promptCircleApi.usePrompt(id);
      
      if ('error' in result) {
        return { error: result.error };
      }

      return { usage_count: result.usage_count, content: result.content };
    } catch (error) {
      console.error('Error using prompt from API:', error);
      return { error: 'Failed to use prompt' };
    }
  }
}

// Factory to get appropriate provider
export async function getLibraryProvider(): Promise<LibraryDataProvider> {
  const authState = await getAuthState();
  
  if (authState.isAuthenticated) {
    return new PromptCircleApiProvider();
  }
  
  return new LocalStorageProvider();
}

// Helper to check if user is in API mode
export async function isApiMode(): Promise<boolean> {
  const authState = await getAuthState();
  return authState.isAuthenticated;
} 