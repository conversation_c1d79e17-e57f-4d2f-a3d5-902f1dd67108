import { promptCircle<PERSON>pi } from './promptCircleApi';
import {
  getBookmarks,
  removeBookmark,
  type Bookmark,
} from './bookmark';

// Unified interface for saved responses (works for both local and API sources)
export interface UnifiedSavedResponse {
  id: string;
  title: string;
  prompt: string; // May be empty for local bookmarks (unknown)
  response: string; // Shown as content in UI
  tags: string[];
  folder?: string;
  note?: string;
  llm_source: 'ChatGPT' | 'Claude' | 'Copilot' | 'Gemini' | 'Grok' | string;
  is_favorite?: boolean;
  created_at: string; // ISO string
  source: 'local' | 'api';
}

export interface SavedResponsesListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: UnifiedSavedResponse[];
}

export interface SavedResponsesQuery {
  favorite?: boolean;
  folder?: string;
  llm_source?: 'ChatGPT' | 'Claude' | 'Copilot' | 'Gemini' | 'Grok';
  page?: number;
  page_size?: number;
  search?: string;
  sort?: 'created' | 'recent' | 'title';
}

export interface CreateSavedResponseInput {
  title: string;
  prompt: string;
  response: string;
  tags?: string[];
  folder?: string;
  note?: string;
  llm_source: 'ChatGPT' | 'Claude' | 'Copilot' | 'Gemini' | 'Grok';
  is_favorite?: boolean;
}

export interface UpdateSavedResponseInput {
  title?: string;
  prompt?: string;
  response?: string;
  tags?: string[];
  folder?: string;
  note?: string;
  llm_source?: 'ChatGPT' | 'Claude' | 'Copilot' | 'Gemini' | 'Grok';
  is_favorite?: boolean;
}

export interface SavedResponsesDataProvider {
  getResponses(params?: SavedResponsesQuery): Promise<SavedResponsesListResponse>;
  createResponse(input: CreateSavedResponseInput): Promise<UnifiedSavedResponse>;
  updateResponse(id: string, input: UpdateSavedResponseInput): Promise<UnifiedSavedResponse>;
  deleteResponse(id: string): Promise<void>;
}

// Converters
function convertApiToUnified(item: any): UnifiedSavedResponse {
  return {
    id: (item.id ?? '').toString(),
    title: item.title ?? 'Untitled',
    prompt: item.prompt ?? '',
    response: item.response ?? '',
    tags: Array.isArray(item.tags) ? item.tags : [],
    folder: item.folder ?? undefined,
    note: item.note ?? undefined,
    llm_source: item.llm_source ?? 'ChatGPT',
    is_favorite: !!item.is_favorite,
    created_at: item.created_at ?? new Date().toISOString(),
    source: 'api',
  };
}

function convertLocalToUnified(b: Bookmark): UnifiedSavedResponse {
  return {
    id: b.id,
    title: b.title || 'Untitled',
    prompt: '',
    response: b.content,
    tags: b.tags || [],
    folder: undefined,
    note: b.note,
    llm_source: (b.source as any) || 'ChatGPT',
    is_favorite: false,
    created_at: new Date(b.timestamp).toISOString(),
    source: 'local',
  };
}

// Local provider (uses existing bookmarks logic; create/update are not supported in popup)
export class LocalSavedResponsesProvider implements SavedResponsesDataProvider {
  async getResponses(params?: SavedResponsesQuery): Promise<SavedResponsesListResponse> {
    const all = await getBookmarks();
    // Apply basic client-side filters to mirror API semantics
    let filtered = all;
    if (params?.llm_source) {
      filtered = filtered.filter(b => b.source === params.llm_source);
    }
    if (params?.search) {
      const q = params.search.toLowerCase();
      filtered = filtered.filter(b =>
        (b.title?.toLowerCase().includes(q)) ||
        (b.content?.toLowerCase().includes(q)) ||
        (b.note?.toLowerCase().includes(q)) ||
        (b.tags || []).some(t => t.toLowerCase().includes(q))
      );
    }
    const results = filtered
      .map(convertLocalToUnified)
      .sort((a, b) => b.created_at.localeCompare(a.created_at));

    return {
      count: results.length,
      next: null,
      previous: null,
      results,
    };
  }

  async createResponse(): Promise<UnifiedSavedResponse> {
    throw new Error('Creating saved responses is not supported in local mode here. Use in-page bookmark flow.');
  }

  async updateResponse(): Promise<UnifiedSavedResponse> {
    throw new Error('Updating saved responses is not supported in local mode.');
  }

  async deleteResponse(id: string): Promise<void> {
    await removeBookmark(id);
  }
}

// API provider
export class PromptCircleSavedResponsesProvider implements SavedResponsesDataProvider {
  async getResponses(params?: SavedResponsesQuery): Promise<SavedResponsesListResponse> {
    const resp = await promptCircleApi.getSavedResponses(params || {});
    if ('error' in resp) {
      // In error case, return empty list for UI stability
      return { count: 0, next: null, previous: null, results: [] };
    }
    return {
      count: resp.count,
      next: resp.next,
      previous: resp.previous,
      results: (resp.results || []).map(convertApiToUnified),
    };
  }

  async createResponse(input: CreateSavedResponseInput): Promise<UnifiedSavedResponse> {
    const resp = await promptCircleApi.createSavedResponse(input);
    if ('error' in resp) throw new Error(resp.error);
    return convertApiToUnified(resp);
  }

  async updateResponse(id: string, input: UpdateSavedResponseInput): Promise<UnifiedSavedResponse> {
    const resp = await promptCircleApi.updateSavedResponse(id, input);
    if ('error' in resp) throw new Error(resp.error);
    return convertApiToUnified(resp);
  }

  async deleteResponse(id: string): Promise<void> {
    const resp = await promptCircleApi.deleteSavedResponse(id);
    if ('error' in resp) throw new Error(resp.error);
  }
}

export async function getSavedResponsesProvider(): Promise<SavedResponsesDataProvider> {
  try {
    const { isAuthenticated } = await promptCircleApi.checkAuth();
    if (isAuthenticated) return new PromptCircleSavedResponsesProvider();
  } catch {}
  return new LocalSavedResponsesProvider();
}


