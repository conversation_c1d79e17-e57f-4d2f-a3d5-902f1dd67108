import { useState, useEffect } from "react";
import {
  <PERSON>ton,
  CircularProgress,
  <PERSON>po<PERSON>,
  <PERSON>,
  Alert,
  <PERSON>nackbar,
  <PERSON><PERSON>,
  <PERSON>b,
  IconButton,
} from "@mui/material";
import GoogleIcon from "@mui/icons-material/Google";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import {
  handleSignOut,
  getAuthState,
  clearPromptCircleAuthState,
} from "@/utils/auth";
import type { AuthState } from "@/types/auth";
import { getLibraryProvider } from "@/utils/libraryData";
import SavedResponses from "./SavedResponses";
import "./App.css";
import SettingsIcon from "@mui/icons-material/Settings";
import CloseIcon from "@mui/icons-material/Close";
import LibraryBooksIcon from "@mui/icons-material/LibraryBooks";
import ForumIcon from "@mui/icons-material/Forum";
import ChatIcon from "@mui/icons-material/Chat";
import GroupIcon from "@mui/icons-material/Group";
import Logo from "@/assets/react.svg";
import PromptPanel from "./PromptPanel";
import LibraryPanel from "./LibraryPanel";
import CommunityPanel from "./CommunityPanel";
import SettingsPanel from "./SettingsPanel";
import LoginInterface from "./LoginInterface";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 0 }}>{children}</Box>}
    </div>
  );
}

// TopBar component (inline for now)
function TopBar({
  onSettings,
  onClose,
  onBack,
  user,
  anchorEl,
  open,
  handleUserIconClick,
  handleMenuClose,
  handleLogoutClick,
}: {
  onSettings: () => void;
  onClose: () => void;
  onBack?: () => void;
  user?: any;
  anchorEl: null | HTMLElement;
  open: boolean;
  handleUserIconClick: (event: React.MouseEvent<HTMLElement>) => void;
  handleMenuClose: () => void;
  handleLogoutClick: () => void;
}) {
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        height: 48,
        px: 1,
        borderBottom: "1px solid #e0e0e0",
        background: "#fff",
        position: "sticky",
        top: 0,
        zIndex: 10,
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {/* {onBack && (
          <IconButton size="small" onClick={onBack} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
        )} */}
        <img
          src={chrome.runtime.getURL("/icon/48.png")}
          alt="PromptCircle Logo"
          style={{ height: 32, width: 32 }}
        />
        <Typography
          variant="h6"
          sx={{ fontWeight: 700, color: "#e0904e", fontSize: "1.1rem" }}
        >
          PromptCircle
        </Typography>
      </Box>
      <Box>
        {user && (
          <>
            <IconButton
              size="small"
              onClick={handleUserIconClick}
              sx={{ p: 0, mr: 1 }}
            >
              <AccountCircleIcon fontSize="large" />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleMenuClose}
              anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
              transformOrigin={{ vertical: "top", horizontal: "right" }}
            >
              <MenuItem onClick={handleLogoutClick}>Log out</MenuItem>
            </Menu>
          </>
        )}
        <IconButton size="small" onClick={onSettings}>
          <SettingsIcon />
        </IconButton>
        <IconButton size="small" onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>
    </Box>
  );
}

export default function App() {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    userInfo: null,
    accessToken: null,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [promptPanelValue, setPromptPanelValue] = useState("");
  const [showLoginInterface, setShowLoginInterface] = useState(true);

  // User menu state
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  // Get user info for display
  const user = authState.userInfo;

  // Initialize auth state and set up listeners
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        console.log("Loading initial auth state...");
        const state = await getAuthState();
        console.log("Initial auth state:", state);
        if (mounted) {
          setAuthState(state);
        }
      } catch (error) {
        console.error("Error loading auth state:", error);
        if (mounted) {
          setError("Failed to load authentication state");
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    // Handle storage changes
    const handleStorageChange = (changes: {
      [key: string]: chrome.storage.StorageChange;
    }) => {
      if (changes.authState && mounted) {
        console.log(
          "Auth state changed from storage:",
          changes.authState.newValue
        );
        const newState = changes.authState.newValue || {
          isAuthenticated: false,
          userInfo: null,
          accessToken: null,
        };
        setAuthState(newState);

        // Show success message when user logs in
        // if (newState.isAuthenticated && !authState.isAuthenticated) {
        //   if (newState.accessToken) {
        //     setSuccess("Successfully signed in with Google!");
        //   } else {
        //     setSuccess("Successfully signed in to PromptCircle!");
        //   }
        // }
      }
    };

    // Handle messages from background service worker
    const handleMessage = (message: any) => {
      if (!mounted) return;

      if (message.type === "AUTH_STATE_CHANGED") {
        console.log("Auth state changed from message:", message.authState);
        const newState = message.authState || {
          isAuthenticated: false,
          userInfo: null,
          accessToken: null,
        };
        setAuthState(newState);

        // Show success message when user logs in
        // if (newState.isAuthenticated && !authState.isAuthenticated) {
        //   if (newState.accessToken) {
        //     setSuccess("Successfully signed in with Google!");
        //   } else {
        //     setSuccess("Successfully signed in to PromptCircle!");
        //   }
        // }
      } else if (message.type === "GOOGLE_LOGIN_RESULT") {
        if (message.success) {
          console.log("Login successful:", message.authState);
          setAuthState(message.authState);
          setSuccess("Successfully signed in!");
        } else {
          console.error("Login failed:", message.error);
          setError(message.error || "Failed to sign in with Google");
        }
        setLoading(false);
      }
    };

    // Initialize auth state
    initializeAuth();

    // Set up listeners
    chrome.storage.local.onChanged.addListener(handleStorageChange);
    browser.runtime.onMessage.addListener(handleMessage);

    // Cleanup function
    return () => {
      mounted = false;
      chrome.storage.local.onChanged.removeListener(handleStorageChange);
      // Note: browser.runtime.onMessage doesn't have removeListener in types
      // but it's safe to keep this for future compatibility
      (browser.runtime.onMessage as any).removeListener?.(handleMessage);
    };
  }, []); // Empty dependency array since we want this to run once on mount

  // Handle login success - immediate UI update, then fetch user info
  const handleLoginSuccess = async (newAuthState?: AuthState) => {
    try {
      // If newAuthState is provided (Google OAuth), use it directly
      if (newAuthState) {
        setAuthState(newAuthState);
        return;
      }

      // For PromptCircle login, immediately set authenticated state to hide login interface
      const tempAuthState: AuthState = {
        isAuthenticated: true,
        userInfo: null, // Will be populated after API call
        accessToken: null, // PromptCircle uses session-based auth
      };

      setAuthState(tempAuthState);
      setSuccess("Successfully signed in to PromptCircle!");

      // Continue fetching user info in the background with AbortController
      const abortController = new AbortController();

      // Clean up on component unmount
      const cleanup = () => {
        abortController.abort();
      };

      // Add cleanup to window beforeunload (when popup closes)
      window.addEventListener("beforeunload", cleanup);

      try {
        const response = await fetch(
          "https://www.promptcircle.ai/_allauth/browser/v1/auth/session",
          {
            credentials: "include",
            signal: abortController.signal,
          }
        );

        if (response.ok) {
          const data = await response.json();

          if (data.status === 200 && data.meta.is_authenticated) {
            const userInfo = {
              sub: data.data.user.id.toString(),
              email: data.data.user.email,
              name: data.data.user.display,
              picture: data.data.user.avatar_url,
            };

            const updatedAuthState: AuthState = {
              isAuthenticated: true,
              userInfo: userInfo,
              accessToken: null,
            };

            setAuthState(updatedAuthState);

            // Save to storage
            await chrome.storage.local.set({ authState: updatedAuthState });
          } else {
            console.warn(
              "User info fetch failed, but session is authenticated"
            );
          }
        } else {
          console.warn(
            `API request failed: ${response.status}, but session is authenticated`
          );
        }
      } catch (apiError) {
        // Only log error if it's not an abort error (which happens when popup closes)
        if (apiError instanceof Error && apiError.name !== "AbortError") {
          console.error("Error fetching user info (background):", apiError);
        }
        // Don't show error to user since they're already authenticated
      } finally {
        // Clean up event listener
        window.removeEventListener("beforeunload", cleanup);
      }
    } catch (error) {
      console.error("Error in handleLoginSuccess:", error);
      setError("Failed to authenticate. Please try again.");
    }
  };

  const handleOpenOptimizer = () => {
    // No need to manually set showLoginInterface to false - it will be handled automatically
    // by the shouldShowLogin logic based on authentication state
    setShowLoginInterface(false);
  };

  const handleBackToLogin = () => {
    setShowLoginInterface(true);
  };

  // Handler for closing the popup (window.close for now)
  const handleClose = () => {
    window.close();
  };

  // Handler for opening settings
  const handleSettings = () => {
    setShowSettings(true);
  };

  // Handler for closing settings
  const handleCloseSettings = () => {
    setShowSettings(false);
  };

  // Handler to set prompt from Library/Community
  const handleSetPrompt = (value: string) => {
    setActiveTab(0);
    setPromptPanelValue(value);
  };
  // Handler to save to library from Community
  const handleSaveToLibrary = async (title: string, content: string) => {
    try {
      // Check authentication status
      const authState = await getAuthState();
      
      if (authState.isAuthenticated) {
        // User is logged in - save to API
        const dataProvider = await getLibraryProvider();
        await dataProvider.savePrompt({
          title,
          content,
          tags: [],
          folder: '',
          compatible_llms: ['ChatGPT'],
          isFavorite: false
        });
      } else {
        // User is not logged in - save to local storage
        chrome.storage.local.get(["promptLibrary"], (result) => {
          const lib = Array.isArray(result.promptLibrary)
            ? result.promptLibrary
            : [];
          const updated = [{ id: Date.now().toString(), title, content }, ...lib];
          chrome.storage.local.set({ promptLibrary: updated });
        });
      }
    } catch (error) {
      console.error('Error saving prompt to library:', error);
    }
  };

  // User menu handlers
  const handleUserIconClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Logout handler - similar to LoginInterface handlePromptCircleLogout
  const handleLogoutClick = async () => {
    handleMenuClose(); // Close the menu first

    try {
      // Handle Google logout
      if (authState.isAuthenticated) {
        await handleSignOut();
      }

      // Handle PromptCircle logout (when accessToken is null, it's PromptCircle auth)
      if (authState.isAuthenticated && !authState.accessToken) {
        const response = (await chrome.runtime.sendMessage({
          type: "PROMPTCIRCLE_LOGOUT",
        })) as unknown as { success: boolean };

        if (response && response.success) {
          // Clear PromptCircle auth state from storage
          await clearPromptCircleAuthState();
        }
      }

      // Show login interface after logout
      setShowLoginInterface(true);
      setSuccess("Successfully logged out!");
    } catch (error) {
      console.error("Error during logout:", error);
      setError("Failed to logout. Please try again.");
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          width: "350px",
          padding: "16px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "200px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Show login interface only if user is not authenticated and showLoginInterface is true
  const shouldShowLogin = showLoginInterface && !authState.isAuthenticated;

  if (shouldShowLogin) {
    return (
      <LoginInterface
        onLoginSuccess={handleLoginSuccess}
        onOpenOptimizer={handleOpenOptimizer}
      />
    );
  }

  // Show settings panel if settings is open
  if (showSettings) {
    return <SettingsPanel onClose={handleCloseSettings} />;
  }

  return (
    <Box
      sx={{
        width: "400px",
        minHeight: "600px",
        display: "flex",
        flexDirection: "column",
        background: "#fafafa",
        fontFamily: "Arial, sans-serif",
        boxShadow: 2,
        borderRadius: 2,
        overflow: "hidden",
        position: "relative",
      }}
    >
      <TopBar
        onSettings={handleSettings}
        onClose={handleClose}
        onBack={handleBackToLogin}
        user={user}
        anchorEl={anchorEl}
        open={open}
        handleUserIconClick={handleUserIconClick}
        handleMenuClose={handleMenuClose}
        handleLogoutClick={handleLogoutClick}
      />
      {error && (
        <Alert severity="error" onClose={() => setError(null)} sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      <Snackbar
        open={!!success}
        autoHideDuration={3000}
        onClose={() => setSuccess(null)}
        message={success}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
        ContentProps={{ style: { background: "#388e3c", color: "#fff" } }}
        action={
          <Button
            size="small"
            onClick={() => setSuccess(null)}
            sx={{ color: "#ffffff", minWidth: "auto" }}
          >
            <CloseIcon fontSize="small" />
          </Button>
        }
      />
      {/* Tab Bar */}
      <Tabs
        value={activeTab}
        onChange={(_, v) => setActiveTab(v)}
        variant="fullWidth"
        sx={{ borderBottom: "1px solid #e0e0e0", background: "#fff" }}
        aria-label="PromptCircle main tabs"
      >
        <Tab icon={<ChatIcon />} label="Optimize Prompt" />
        <Tab icon={<ForumIcon />} label="Saved Responses" />
        <Tab icon={<LibraryBooksIcon />} label="Prompt Library" />
        <Tab icon={<GroupIcon />} label="Community" />
      </Tabs>
      {/* Main Content */}
      <Box sx={{ flex: 1, overflow: "auto", background: "#fafafa" }}>
        <TabPanel value={activeTab} index={0}>
          <PromptPanel
            key={activeTab}
            initialPrompt={promptPanelValue}
            setPromptPanelValue={setPromptPanelValue}
          />
        </TabPanel>
        <TabPanel value={activeTab} index={1}>
          <SavedResponses />
        </TabPanel>
        <TabPanel value={activeTab} index={2}>
          <LibraryPanel onUsePrompt={handleSetPrompt} />
        </TabPanel>
        <TabPanel value={activeTab} index={3}>
          <CommunityPanel
            onUsePrompt={handleSetPrompt}
            onSaveToLibrary={handleSaveToLibrary}
          />
        </TabPanel>
      </Box>
    </Box>
  );
}
