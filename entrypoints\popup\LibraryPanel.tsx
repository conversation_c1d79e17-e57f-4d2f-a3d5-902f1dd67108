import React, { useState, useEffect } from 'react';
import { <PERSON>ton, <PERSON>po<PERSON>, <PERSON>, Snackbar, Modal, Chip, IconButton, Card, CardContent, Tooltip, TextField, Select, MenuItem, InputLabel, FormControl, Divider, CircularProgress } from '@mui/material';
import PromptSaveModal, { PromptData } from './PromptSaveModal';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SendIcon from '@mui/icons-material/Send';
import StarIcon from '@mui/icons-material/Star';
import StarBorderIcon from '@mui/icons-material/StarBorder';
import LabelIcon from '@mui/icons-material/Label';
import FolderIcon from '@mui/icons-material/Folder';
import CloseIcon from '@mui/icons-material/Close';
import CopyIcon from '@mui/icons-material/ContentCopy';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import { getLibraryProvider, isApiMode as checkApiMode, UnifiedPrompt, LibraryDataProvider } from '../../utils/libraryData';
import { getAuthState } from '../../utils/auth';

export default function LibraryPanel({ onUsePrompt }: { onUsePrompt: (prompt: string) => void }) {
  // Unified state for both local and API prompts
  const [prompts, setPrompts] = useState<UnifiedPrompt[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Auth and provider state
  const [dataProvider, setDataProvider] = useState<LibraryDataProvider | null>(null);
  const [isApiMode, setIsApiModeState] = useState(false);
  
  // Pagination state (for API mode)
  const [pagination, setPagination] = useState({
    currentPage: 1,
    hasNext: false,
    hasPrev: false,
    totalCount: 0
  });

  // Filter state
  const [search, setSearch] = useState('');
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [selectedTag, setSelectedTag] = useState<string>('');
  const [selectedLLM, setSelectedLLM] = useState<'All LLMs' | 'ChatGPT' | 'Claude' | 'Copilot' | 'Gemini' | 'Grok'>('All LLMs');
  const [sortBy, setSortBy] = useState<'created' | 'recent' | 'title' | 'usage'>('created');
  const [favoriteFilter, setFavoriteFilter] = useState<boolean | null>(null);

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'add'|'edit'>('add');
  const [modalInitial, setModalInitial] = useState<Partial<PromptData>>({});
  const [editingId, setEditingId] = useState<string | null>(null);

  // Snackbar state
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({ open: false, message: '', severity: 'success' });

  // Individual loading states for better UX
  const [loadingStates, setLoadingStates] = useState<{ [key: string]: boolean }>({});

  // Confirmation dialog state
  const [deleteConfirmation, setDeleteConfirmation] = useState<{ open: boolean; promptId: string | null; promptTitle: string }>({ open: false, promptId: null, promptTitle: '' });

  // Initialize provider and load prompts
  useEffect(() => {
    const initializeProvider = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const provider = await getLibraryProvider();
        setDataProvider(provider);
        
        const apiMode = await checkApiMode();
        setIsApiModeState(apiMode);
        
        await loadPrompts();
      } catch (error) {
        console.error('Error initializing provider:', error);
        setError(error instanceof Error ? error.message : 'Failed to initialize');
      } finally {
        setLoading(false);
      }
    };

    initializeProvider();
  }, []);

  // Load prompts with current filters
  const loadPrompts = async (page: number = 1) => {
    if (!dataProvider) return;

    try {
      setLoading(true);
      setError(null);

      // Build params object, only include compatible_llm if user has selected a specific filter
      const params: any = {
        search,
        folder: selectedFolder,
        sort: sortBy,
        favorite: favoriteFilter,
        page,
        page_size: 10
      };

      // Only include compatible_llm if user has selected a specific LLM (not "All LLMs")
      if (selectedLLM && selectedLLM !== 'All LLMs') {
        params.compatible_llm = selectedLLM;
      }

      console.log('Loading prompts with params:', params);
      const result = await dataProvider.getPrompts(params);
      setPrompts(result.prompts);

      if (result.pagination) {
        setPagination({
          currentPage: result.pagination.currentPage,
          hasNext: !!result.pagination.next,
          hasPrev: !!result.pagination.previous,
          totalCount: result.pagination.count
        });
      }
    } catch (error) {
      console.error('Error loading prompts:', error);
      setError(error instanceof Error ? error.message : 'Failed to load prompts');
      setSnackbar({ open: true, message: 'Failed to load prompts', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Reload prompts when filters change
  useEffect(() => {
    if (dataProvider) {
      loadPrompts(1);
    }
  }, [search, selectedFolder, selectedTag, selectedLLM, sortBy, favoriteFilter, dataProvider]);

  // Open modal for add or edit
  const openAddModal = () => {
    setModalInitial({});
    setModalMode('add');
    setModalOpen(true);
  };

  const openEditModal = (item: UnifiedPrompt) => {
    setModalInitial({
      title: item.title,
      content: item.content,
      tags: item.tags,
      folder: item.folder,
      optimize: ''
    });
    setEditingId(item.id);
    setModalMode('edit');
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
    setEditingId(null);
  };

  const handleSaveModal = async (data: PromptData) => {
    if (!dataProvider) return;

    try {
      if (modalMode === 'edit' && editingId) {
        // Set loading state for edit
        setLoadingStates(prev => ({ ...prev, [`edit-${editingId}`]: true }));

        // Optimistic update for edit
        setPrompts(prevPrompts => 
          prevPrompts.map(prompt => 
            prompt.id === editingId 
              ? { 
                  ...prompt, 
                  title: data.title,
                  content: data.content,
                  tags: data.tags || [],
                  folder: data.folder || '',
                  compatible_llms: data.compatible_llms || [],
                  updated_at: new Date().toISOString()
                }
              : prompt
          )
        );

        await dataProvider.updatePrompt(editingId, data);
        setSnackbar({ open: true, message: 'Prompt updated!', severity: 'success' });
      } else {
        // Optimistic update for new prompt
        const newPrompt: UnifiedPrompt = {
          id: `temp-${Date.now()}`, // Temporary ID
          title: data.title,
          content: data.content,
          tags: data.tags || [],
          folder: data.folder || '',
          compatible_llms: data.compatible_llms || [],
          isFavorite: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          source: isApiMode ? 'api' : 'local'
        };

        setPrompts(prevPrompts => [newPrompt, ...prevPrompts]);
        await dataProvider.savePrompt(data);
        setSnackbar({ open: true, message: 'Prompt added!', severity: 'success' });
      }
      
      setModalOpen(false);
      setEditingId(null);
    } catch (error) {
      console.error('Error saving prompt:', error);
      
      // Revert optimistic updates on error
      if (modalMode === 'edit' && editingId) {
        // Reload to revert the edit
        await loadPrompts(pagination.currentPage);
      } else {
        // Remove the temporary prompt
        setPrompts(prevPrompts => prevPrompts.filter(p => !p.id.startsWith('temp-')));
      }
      
      setSnackbar({ open: true, message: 'Failed to save prompt', severity: 'error' });
    } finally {
      // Clear loading states
      if (modalMode === 'edit' && editingId) {
        setLoadingStates(prev => ({ ...prev, [`edit-${editingId}`]: false }));
      }
    }
  };

  // Handler to toggle favorite
  const handleToggleFavorite = async (id: string) => {
    if (!dataProvider) return;

    // Set individual loading state
    setLoadingStates(prev => ({ ...prev, [`favorite-${id}`]: true }));

    try {
      // Optimistic update - immediately update the UI
      setPrompts(prevPrompts => 
        prevPrompts.map(prompt => 
          prompt.id === id 
            ? { ...prompt, isFavorite: !prompt.isFavorite }
            : prompt
        )
      );

      // Make the API call
      await dataProvider.toggleFavorite(id);
      
      // Show success message
      setSnackbar({ open: true, message: 'Favorite toggled!', severity: 'success' });
    } catch (error) {
      console.error('Error toggling favorite:', error);
      
      // Revert optimistic update on error
      setPrompts(prevPrompts => 
        prevPrompts.map(prompt => 
          prompt.id === id 
            ? { ...prompt, isFavorite: !prompt.isFavorite } // Revert the change
            : prompt
        )
      );
      
      setSnackbar({ open: true, message: 'Failed to toggle favorite', severity: 'error' });
    } finally {
      // Clear individual loading state
      setLoadingStates(prev => ({ ...prev, [`favorite-${id}`]: false }));
    }
  };

  // Handler to delete a prompt
  const handleDelete = async (id: string) => {
    const prompt = prompts.find(p => p.id === id);
    if (prompt) {
      setDeleteConfirmation({ open: true, promptId: id, promptTitle: prompt.title });
    }
  };

  // Handler to confirm delete
  const handleConfirmDelete = async () => {
    const { promptId } = deleteConfirmation;
    if (!promptId || !dataProvider) return;

    // Set individual loading state
    setLoadingStates(prev => ({ ...prev, [`delete-${promptId}`]: true }));

    // Store the prompt to be deleted for potential rollback
    const deletedPrompt = prompts.find(p => p.id === promptId);

    try {
      // Optimistic update - immediately remove from UI
      setPrompts(prevPrompts => prevPrompts.filter(prompt => prompt.id !== promptId));

      // Make the API call
      await dataProvider.deletePrompt(promptId);
      
      // Show success message
      setSnackbar({ open: true, message: 'Prompt deleted.', severity: 'success' });
    } catch (error) {
      console.error('Error deleting prompt:', error);
      
      // Revert optimistic update on error
      if (deletedPrompt) {
        setPrompts(prevPrompts => [...prevPrompts, deletedPrompt]);
      }
      
      setSnackbar({ open: true, message: 'Failed to delete prompt', severity: 'error' });
    } finally {
      // Clear individual loading state
      setLoadingStates(prev => ({ ...prev, [`delete-${promptId}`]: false }));
      // Close confirmation dialog
      setDeleteConfirmation({ open: false, promptId: null, promptTitle: '' });
    }
  };

  // Handler to copy prompt content
  const handleCopyContent = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setSnackbar({ open: true, message: 'Prompt copied to clipboard!', severity: 'success' });
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to copy prompt.', severity: 'error' });
    }
  };

  // Handler to send prompt to LLM
  const handleSend = async (promptContent: string, promptId?: string) => {
    if (!promptContent.trim()) {
      setSnackbar({ open: true, message: 'Prompt is empty.', severity: 'error' });
      return;
    }

    // Track usage if user is authenticated and promptId is provided
    if (isApiMode && promptId) {
      try {
        const provider = await getLibraryProvider();
        const usageResult = await provider.usePrompt(promptId);
        
        if ('error' in usageResult) {
          console.warn('Failed to track prompt usage:', usageResult.error);
        } else {
          console.log('Prompt usage tracked successfully. New usage count:', usageResult.usage_count);
        }
      } catch (error) {
        console.error('Error tracking prompt usage:', error);
      }
    }

    // If "All LLMs" is selected, default to ChatGPT
    const targetLLM = selectedLLM === 'All LLMs' ? 'ChatGPT' : selectedLLM;

    let url = '';
    let selector = '';

    switch (targetLLM) {
      case 'ChatGPT':
        url = 'https://chat.openai.com/';
        selector = 'textarea[data-id="root"], textarea[placeholder*="Message"], textarea[placeholder*="Send a message"]';
        break;
      case 'Gemini':
        url = 'https://gemini.google.com/app';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      case 'Claude':
        url = 'https://claude.ai/';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      case 'Copilot':
        url = 'https://copilot.microsoft.com/';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      case 'Grok':
        url = 'https://grok.com/';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      default:
        setSnackbar({ open: true, message: 'Invalid LLM selection.', severity: 'error' });
        return;
    }

    setSnackbar({ open: true, message: `Opening ${targetLLM}...`, severity: 'success' });

    chrome.runtime.sendMessage({
      action: 'openAndFillPrompt',
      url,
      prompt: promptContent,
      selector
    }, (response) => {
      if (response && response.success) {
        setSnackbar({ open: true, message: `Prompt filled in ${targetLLM}!`, severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response && response.error ? response.error : 'Failed to fill prompt.', severity: 'error' });
      }
    });
  };

  // Extract unique folders and tags for filter dropdowns
  const allFolders = Array.from(new Set(prompts.map(item => item.folder).filter(Boolean)));
  const allTags = Array.from(new Set(prompts.flatMap(item => item.tags || [])));

  // Filtered prompts (client-side filtering for local mode, server-side for API mode)
  const filtered = isApiMode ? prompts : prompts.filter(item => {
    const matchesSearch = search ? 
      item.title.toLowerCase().includes(search.toLowerCase()) ||
      item.content.toLowerCase().includes(search.toLowerCase()) ||
      (item.tags && item.tags.some(tag => tag.toLowerCase().includes(search.toLowerCase()))) : true;
    
    const matchesFolder = selectedFolder ? item.folder === selectedFolder : true;
    const matchesTag = selectedTag ? (item.tags && item.tags.includes(selectedTag)) : true;
    const matchesLLM = selectedLLM && selectedLLM !== 'All LLMs' ? (item.compatible_llms && item.compatible_llms.includes(selectedLLM)) : true;
    
    return matchesSearch && matchesFolder && matchesTag && matchesLLM;
  });

  return (
    <>
      {/* Header: Search & Filters */}
      <Box sx={{ p: 2, pb: 1, borderBottom: '1px solid #e0e0e0', background: '#fff', zIndex: 1 }}>
        <Typography fontWeight={600} fontSize={15} sx={{ mb: 1 }}>
          🔍 Search & Filters {isApiMode && '(API Mode)'}
        </Typography>
        <TextField
          value={search}
          onChange={e => setSearch(e.target.value)}
          placeholder="Search prompts by keyword..."
          size="small"
          fullWidth
          InputProps={{
            startAdornment: <span role="img" aria-label="search">🔎</span>,
            style: { borderRadius: 8 }
          }}
          sx={{ mb: 1 }}
        />
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {/* Tag Filter */}
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🏷️ Tag</InputLabel>
            <Select
              value={selectedTag}
              label="🏷️ Tag"
              onChange={e => setSelectedTag(e.target.value)}
            >
              <MenuItem value="">All Tags</MenuItem>
              {allTags.map(tag => (
                <MenuItem key={tag} value={tag}>{tag}</MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* LLM Filter */}
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🤖 LLM</InputLabel>
            <Select
              value={selectedLLM}
              label="🤖 LLM"
              onChange={e => setSelectedLLM(e.target.value as any)}
            >
              <MenuItem value="All LLMs">All LLMs</MenuItem>
              <MenuItem value="ChatGPT">ChatGPT</MenuItem>
              <MenuItem value="Claude">Claude</MenuItem>
              <MenuItem value="Copilot">Copilot</MenuItem>
              <MenuItem value="Gemini">Gemini</MenuItem>
              <MenuItem value="Grok">Grok</MenuItem>
            </Select>
          </FormControl>
          {/* Sort Filter */}
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>📅 Sort</InputLabel>
            <Select
              value={sortBy}
              label="📅 Sort"
              onChange={e => setSortBy(e.target.value as any)}
            >
              <MenuItem value="created">Created</MenuItem>
              <MenuItem value="recent">Recent</MenuItem>
              <MenuItem value="title">Title</MenuItem>
              <MenuItem value="usage">Usage</MenuItem>
            </Select>
          </FormControl>
          {/* Folder Filter */}
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🗂️ Folder</InputLabel>
            <Select
              value={selectedFolder}
              label="🗂️ Folder"
              onChange={e => setSelectedFolder(e.target.value)}
            >
              <MenuItem value="">All Folders</MenuItem>
              {allFolders.map(folder => (
                <MenuItem key={folder} value={folder}>{folder}</MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* Favorite Filter (API mode only) */}
          {isApiMode && (
            <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
              <InputLabel>⭐ Favorite</InputLabel>
              <Select
                value={favoriteFilter === null ? '' : favoriteFilter}
                label="⭐ Favorite"
                onChange={e => setFavoriteFilter(e.target.value === '' ? null : e.target.value === 'true')}
              >
                <MenuItem value="">All Prompts</MenuItem>
                <MenuItem value="true">Favorites Only</MenuItem>
                <MenuItem value="false">Not Favorites</MenuItem>
              </Select>
            </FormControl>
          )}
        </Box>
      </Box>

      {/* Add New Prompt Button */}
      <Box sx={{ p: 2, pt: 1, background: '#fafafa' }}>
        <Button 
          variant="contained" 
          color="primary" 
          onClick={openAddModal} 
          sx={{ alignSelf: 'flex-end', borderRadius: 2, fontWeight: 600, fontSize: 12 }}
        >
          + ADD NEW PROMPT
        </Button>
      </Box>

      {/* Prompt List */}
      <Box sx={{ flex: 1, overflowY: 'auto', background: '#f9fafb', p: 2, display: 'flex', flexDirection: 'column', gap: 2, minHeight: 0 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
            <CircularProgress />
            <Typography sx={{ ml: 2 }}>Loading prompts...</Typography>
          </Box>
        ) : error ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography color="error" sx={{ mb: 2 }}>{error}</Typography>
            <Button variant="contained" onClick={() => loadPrompts(1)}>Retry</Button>
          </Box>
        ) : filtered.length === 0 ? (
          <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center' }}>
            {isApiMode ? 'No prompts found in your PromptCircle library.' : 'No prompts found.'}
          </Typography>
        ) : (
          <>
            {filtered.map(item => (
              <Card key={item.id} sx={{ 
                borderRadius: 2, 
                border: '1px solid #e0e0e0', 
                boxShadow: 0, 
                p: 0, 
                position: 'relative', 
                overflow: 'visible',
                opacity: (loadingStates[`favorite-${item.id}`] || loadingStates[`delete-${item.id}`] || loadingStates[`edit-${item.id}`]) ? 0.7 : 1,
                transition: 'opacity 0.2s ease-in-out'
              }}>
                {/* Loading overlay */}
                {(loadingStates[`favorite-${item.id}`] || loadingStates[`delete-${item.id}`] || loadingStates[`edit-${item.id}`]) && (
                  <Box sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 2,
                    borderRadius: 2
                  }}>
                    <CircularProgress size={24} />
                  </Box>
                )}
                {item.isFavorite && (
                  <Box sx={{ position: 'absolute', top: 8, right: 8, bgcolor: '#ffd700', color: '#b8860b', px: 1, py: 0.5, borderRadius: 1, fontSize: 11, fontWeight: 700, zIndex: 1 }}>
                    <StarIcon fontSize="inherit" sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} /> FAVORITE
                  </Box>
                )}
                {/* {item.source === 'api' && (
                  <Box sx={{ position: 'absolute', top: 8, left: 8, bgcolor: '#e3f2fd', color: '#1976d2', px: 1, py: 0.5, borderRadius: 1, fontSize: 11, fontWeight: 700, zIndex: 1 }}>
                    🌐 API
                  </Box>
                )} */}
                <CardContent sx={{ p: 2, pb: 1 }}>
                  {/* Header Section */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <span role="img" aria-label="prompt" style={{ fontSize: 20 }}>📝</span>
                    <Typography fontWeight={700} fontSize={15} sx={{ flex: 1 }}>{item.title}</Typography>
                  </Box>
                  {/* Content Preview */}
                  <Typography fontSize={13} color="text.secondary" sx={{ mb: 1, whiteSpace: 'pre-line' }}>
                    {item.content.length > 120 ? item.content.slice(0, 120) + '...' : item.content}
                  </Typography>
                  {/* Tags and LLMs */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center', mb: 1 }}>
                    {item.tags && item.tags.map(tag => (
                      <Chip key={tag} icon={<LabelIcon sx={{ fontSize: 16 }} />} label={tag} size="small" sx={{ bgcolor: '#f3e8ff', color: '#7c3aed', fontWeight: 500, '& .MuiChip-icon': { color: '#a855f7' } }} />
                    ))}
                    {item.compatible_llms && item.compatible_llms.map(llm => (
                      <Chip key={llm} icon={<SmartToyIcon sx={{ fontSize: 16 }} />} label={llm} size="small" sx={{ bgcolor: '#dbeafe', color: '#1e40af', fontWeight: 500, '& .MuiChip-icon': { color: '#3b82f6' } }} />
                    ))}
                  </Box>
                  {/* Metadata Row */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, fontSize: 12, color: '#64748b', mb: 1 }}>
                    <span>Created: {new Date(item.created_at).toLocaleDateString()}</span>
                    {item.updated_at !== item.created_at && (
                      <span>Updated: {new Date(item.updated_at).toLocaleDateString()}</span>
                    )}
                    {item.isFavorite && <span>⭐ Favorite</span>}
                    {item.usage_count !== undefined && <span>📊 Used {item.usage_count} times</span>}
                  </Box>
                  <Divider sx={{ my: 1 }} />
                  {/* Action Buttons */}
                  <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'nowrap', justifyContent: 'flex-end' }}>
                    <Tooltip title={`Send to ${selectedLLM}`}>
                      <IconButton color="primary" size="small" onClick={() => handleSend(item.content, item.id)}>
                        <SendIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Copy to clipboard">
                      <IconButton size="small" onClick={() => handleCopyContent(item.content)}>
                        <CopyIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit prompt">
                      <IconButton 
                        size="small" 
                        onClick={() => openEditModal(item)}
                        disabled={loadingStates[`edit-${item.id}`]}
                      >
                        {loadingStates[`edit-${item.id}`] ? (
                          <CircularProgress size={16} />
                        ) : (
                          <EditIcon />
                        )}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={item.isFavorite ? 'Remove from favorites' : 'Add to favorites'}>
                      <IconButton 
                        size="small" 
                        onClick={() => handleToggleFavorite(item.id)}
                        disabled={loadingStates[`favorite-${item.id}`]}
                      >
                        {loadingStates[`favorite-${item.id}`] ? (
                          <CircularProgress size={16} />
                        ) : item.isFavorite ? (
                          <StarIcon fontSize="small" />
                        ) : (
                          <StarBorderIcon fontSize="small" />
                        )}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete prompt">
                      <IconButton 
                        size="small" 
                        color="error" 
                        onClick={() => handleDelete(item.id)}
                        disabled={loadingStates[`delete-${item.id}`]}
                      >
                        {loadingStates[`delete-${item.id}`] ? (
                          <CircularProgress size={16} />
                        ) : (
                          <DeleteIcon />
                        )}
                      </IconButton>
                    </Tooltip>
                  </Box>
                </CardContent>
              </Card>
            ))}

            {/* Pagination (API mode only) */}
            {isApiMode && (pagination.hasNext || pagination.hasPrev) && (
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 2 }}>
                <Button 
                  variant="outlined" 
                  disabled={!pagination.hasPrev || loading}
                  onClick={() => loadPrompts(pagination.currentPage - 1)}
                >
                  Previous
                </Button>
                <Typography sx={{ display: 'flex', alignItems: 'center' }}>
                  Page {pagination.currentPage} of {Math.ceil(pagination.totalCount / 10)}
                </Typography>
                <Button 
                  variant="outlined" 
                  disabled={!pagination.hasNext || loading}
                  onClick={() => loadPrompts(pagination.currentPage + 1)}
                >
                  Next
                </Button>
              </Box>
            )}
          </>
        )}
      </Box>

      {/* Modal */}
      <PromptSaveModal
        open={modalOpen}
        onClose={closeModal}
        onSave={handleSaveModal}
        initialPrompt={modalInitial}
        allFolders={allFolders}
        mode={modalMode}
      />

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        message={snackbar.message}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        ContentProps={{ style: { background: snackbar.severity === 'error' ? '#d32f2f' : '#388e3c', color: '#fff' } }}
        action={
          <Button
            size="small"
            onClick={() => setSnackbar(s => ({ ...s, open: false }))}
            sx={{ color: '#ffffff', minWidth: 'auto' }}
          >
            <CloseIcon fontSize="small" />
          </Button>
        }
      />

      {/* Delete Confirmation Dialog */}
      <Modal
        open={deleteConfirmation.open}
        onClose={() => setDeleteConfirmation({ open: false, promptId: null, promptTitle: '' })}
        sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        <Box sx={{
          bgcolor: 'background.paper',
          borderRadius: 2,
          p: 3,
          maxWidth: 400,
          width: '90%',
          boxShadow: 24,
          outline: 'none'
        }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Delete Prompt
          </Typography>
          <Typography sx={{ mb: 3, color: 'text.secondary' }}>
            Are you sure you want to delete "{deleteConfirmation.promptTitle}"? This action cannot be undone.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              onClick={() => setDeleteConfirmation({ open: false, promptId: null, promptTitle: '' })}
              disabled={loadingStates[`delete-${deleteConfirmation.promptId}`]}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              color="error"
              onClick={handleConfirmDelete}
              disabled={loadingStates[`delete-${deleteConfirmation.promptId}`]}
            >
              {loadingStates[`delete-${deleteConfirmation.promptId}`] ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                'Delete'
              )}
            </Button>
          </Box>
        </Box>
      </Modal>
    </>
  );
} 