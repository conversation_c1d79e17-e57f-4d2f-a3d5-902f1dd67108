// PromptCircle API integration service
// Based on the demo's background.js API functionality

export interface OptimizationSuggestion {
  label: string;
  content: string;
}

export interface OptimizationResponse {
  suggestions: OptimizationSuggestion[];
}

export interface CommunityPrompt {
  id: number;
  title: string;
  content: string;
  description: string;
  tags: string[];
  category: string;
  category_display: string;
  compatible_llms: string[];
  example_use_case: string;
  upvotes: number;
  usage_count: number;
  is_featured: boolean;
  is_verified: boolean;
  user: {
    username: string;
    first_name: string;
    last_name: string;
    display_name: string;
  };
  user_has_voted: boolean;
  is_own_prompt: boolean;
  created_at: string;
  updated_at: string;
}

export interface CommunityPromptsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: CommunityPrompt[];
}

// New interface for saved prompts
export interface SavedPrompt {
  id: number;
  title: string;
  content: string;
  tags: string[];
  folder: string;
  compatible_llms: string[];
  is_favorite: boolean;
  usage_count: number;
  created_at: string;
  updated_at: string;
}

export interface SavedPromptsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SavedPrompt[];
}

export interface ApiError {
  error: string;
}

export interface UsePromptResponse {
  usage_count: number;
  content: string;
}

export class PromptCircleApiService {
  private baseURL = "https://www.promptcircle.ai";
  private apiURL = "https://www.promptcircle.ai/api";

  /**
   * Check if user is authenticated with PromptCircle
   */
  async checkAuth(): Promise<{ isAuthenticated: boolean }> {
    try {
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const sessionCookie = cookies.find((c) => c.name === "sessionid");

      return { isAuthenticated: !!sessionCookie };
    } catch (error) {
      console.error("Error checking auth:", error);
      return { isAuthenticated: false };
    }
  }

  /**
   * Optimize a prompt using PromptCircle API
   */
  async optimizePrompt(
    prompt: string
  ): Promise<OptimizationResponse | ApiError> {
    try {
      // Check authentication first
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        return { error: "User not authenticated with PromptCircle" };
      }

      // Get CSRF token from cookies
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");

      if (!csrfCookie) {
        return {
          error: "No CSRF token found. Please login to PromptCircle first.",
        };
      }

      // Make API request
      const response = await fetch(`${this.apiURL}/optimize-prompt/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": csrfCookie.value,
        },
        body: JSON.stringify({ prompt }),
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      return data as OptimizationResponse;
    } catch (error) {
      console.error("Error optimizing prompt:", error);
      return {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Get saved prompts with pagination and filtering
   */
  async getSavedPrompts(params?: {
    compatible_llm?: 'ChatGPT' | 'Claude' | 'Copilot' | 'Gemini' | 'Grok';
    favorite?: boolean;
    folder?: string;
    page?: number;
    page_size?: number;
    search?: string;
    sort?: 'created' | 'recent' | 'title' | 'usage';
  }): Promise<SavedPromptsResponse | ApiError> {
    try {
      console.log('getSavedPrompts called with params:', params);
      
      // Check authentication first
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        console.log('User not authenticated');
        return { error: "User not authenticated with PromptCircle" };
      }

      // Get CSRF token from cookies
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");

      if (!csrfCookie) {
        console.log('No CSRF token found');
        return {
          error: "No CSRF token found. Please login to PromptCircle first.",
        };
      }

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params?.compatible_llm) queryParams.append('compatible_llm', params.compatible_llm);
      if (params?.favorite !== undefined && params.favorite !== null) queryParams.append('favorite', params.favorite.toString());
      if (params?.folder) queryParams.append('folder', params.folder);
      if (params?.page && params.page !== null) queryParams.append('page', params.page.toString());
      if (params?.page_size && params.page_size !== null) queryParams.append('page_size', params.page_size.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.sort) queryParams.append('sort', params.sort);

      // Make API request
      const url = `${this.apiURL}/saved-prompts/?${queryParams.toString()}`;
      console.log('Making API request to:', url);
      
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": csrfCookie.value,
        },
        credentials: "include",
      });

      console.log('API response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', errorText);
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      console.log("Saved prompts API response:", data);
      return data as SavedPromptsResponse;
    } catch (error) {
      console.error("Error fetching saved prompts:", error);
      console.error("Error details:", {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        params: params
      });
      return {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Open PromptCircle login page
   */
  openLoginPage(): void {
    chrome.tabs.create({
      url: "https://www.promptcircle.ai/accounts/login/",
    });
  }

  /**
   * Logout from PromptCircle (clear cookies)
   */
  async logout(): Promise<void> {
    try {
      await chrome.cookies.remove({
        url: "https://www.promptcircle.ai",
        name: "sessionid",
      });

      await chrome.cookies.remove({
        url: "https://www.promptcircle.ai",
        name: "csrftoken",
      });
    } catch (error) {
      console.error("Error logging out:", error);
    }
  }

  /**
   * Get user session info from PromptCircle
   * Try multiple endpoints to get user information
   */
  async getSessionInfo(): Promise<any> {
    try {
      // First try the allauth session endpoint
      let response = await fetch(
        `${this.baseURL}/_allauth/browser/v1/auth/session/`,
        {
          credentials: "include",
        }
      );

      if (response.ok) {
        return await response.json();
      }

      // If allauth endpoint fails, try a user profile endpoint
      response = await fetch(`${this.apiURL}/user/profile/`, {
        credentials: "include",
      });

      if (response.ok) {
        return await response.json();
      }

      // If both fail, try to get basic info from cookies and create a minimal user object
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });

      const sessionCookie = cookies.find((c) => c.name === "sessionid");
      if (sessionCookie) {
        // Return a minimal user object based on authentication status
        return {
          user: {
            email: "<EMAIL>", // Placeholder
            username: "PromptCircle User",
            first_name: "",
            last_name: "",
          },
          isAuthenticated: true,
        };
      }

      return null;
    } catch (error) {
      console.error("Error getting session info:", error);
      return null;
    }
  }

  /**
   * Get community prompts with pagination
   */
  async getCommunityPrompts(
    page: number = 1
  ): Promise<CommunityPromptsResponse | ApiError> {
    try {
      // Check authentication first
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        return { error: "User not authenticated with PromptCircle" };
      }

      // Get CSRF token from cookies
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");

      if (!csrfCookie) {
        return {
          error: "No CSRF token found. Please login to PromptCircle first.",
        };
      }

      // Make API request
      const response = await fetch(
        `${this.apiURL}/community-prompts/?page=${page}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": csrfCookie.value,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      console.log("--------data", data);
      return data as CommunityPromptsResponse;
    } catch (error) {
      console.error("Error fetching community prompts:", error);
      return {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  // ===================== SAVED RESPONSES (new) =====================
  /**
   * Types for Saved Responses
   */
  // Using inline interfaces to avoid file-wide exports unless needed externally
  
  /**
   * Get saved responses with pagination and filtering
   */
  async getSavedResponses(params?: {
    favorite?: boolean;
    folder?: string;
    llm_source?: 'ChatGPT' | 'Claude' | 'Copilot' | 'Gemini' | 'Grok';
    page?: number;
    page_size?: number;
    search?: string;
    sort?: 'created' | 'recent' | 'title';
  }): Promise<{
    count: number;
    next: string | null;
    previous: string | null;
    results: Array<{
      id: number;
      title: string;
      prompt: string;
      response: string;
      tags: string[];
      folder: string;
      note: string;
      llm_source: string;
      is_favorite: boolean;
      created_at: string;
    }>;
  } | ApiError> {
    try {
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        return { error: "User not authenticated with PromptCircle" };
      }

      const cookies = await chrome.cookies.getAll({ domain: "promptcircle.ai" });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");
      if (!csrfCookie) {
        return { error: "No CSRF token found. Please login to PromptCircle first." };
      }

      const queryParams = new URLSearchParams();
      if (params?.favorite !== undefined && params.favorite !== null) queryParams.append('favorite', String(params.favorite));
      if (params?.folder) queryParams.append('folder', params.folder);
      if (params?.llm_source) queryParams.append('llm_source', params.llm_source);
      if (params?.page) queryParams.append('page', String(params.page));
      if (params?.page_size) queryParams.append('page_size', String(params.page_size));
      if (params?.search) queryParams.append('search', params.search);
      if (params?.sort) queryParams.append('sort', params.sort);

      const url = `${this.apiURL}/saved-responses/?${queryParams.toString()}`;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfCookie.value,
        },
        credentials: 'include',
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching saved responses:', error);
      return { error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  }

  /**
   * Create saved response
   */
  async createSavedResponse(body: {
    title: string;
    prompt: string;
    response: string;
    tags?: string[];
    folder?: string;
    note?: string;
    llm_source: 'ChatGPT' | 'Claude' | 'Copilot' | 'Gemini' | 'Grok';
    is_favorite?: boolean;
  }): Promise<
    | {
        id: number;
        title: string;
        prompt: string;
        response: string;
        tags: string[];
        folder: string;
        note: string;
        llm_source: string;
        is_favorite: boolean;
        created_at: string;
      }
    | ApiError
  > {
    try {
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        return { error: "User not authenticated with PromptCircle" };
      }

      const cookies = await chrome.cookies.getAll({ domain: "promptcircle.ai" });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");
      if (!csrfCookie) {
        return { error: "No CSRF token found. Please login to PromptCircle first." };
      }

      const response = await fetch(`${this.apiURL}/saved-responses/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfCookie.value,
        },
        body: JSON.stringify(body),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error creating saved response:', error);
      return { error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  }

  /**
   * Update saved response
   */
  async updateSavedResponse(
    id: string,
    body: Partial<{
      title?: string;
      prompt?: string;
      response?: string;
      tags?: string[];
      folder?: string;
      note?: string;
      llm_source?: 'ChatGPT' | 'Claude' | 'Copilot' | 'Gemini' | 'Grok';
      is_favorite?: boolean;
    }>
  ): Promise<
    | {
        id: number;
        title: string;
        prompt: string;
        response: string;
        tags: string[];
        folder: string;
        note: string;
        llm_source: string;
        is_favorite: boolean;
        created_at: string;
      }
    | ApiError
  > {
    try {
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) return { error: 'User not authenticated with PromptCircle' };

      const cookies = await chrome.cookies.getAll({ domain: 'promptcircle.ai' });
      const csrfCookie = cookies.find((c) => c.name === 'csrftoken');
      if (!csrfCookie) return { error: 'No CSRF token found. Please login to PromptCircle first.' };

      const response = await fetch(`${this.apiURL}/saved-responses/${id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfCookie.value,
        },
        body: JSON.stringify(body),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error updating saved response:', error);
      return { error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  }

  /**
   * Delete saved response
   */
  async deleteSavedResponse(id: string): Promise<{ success: true } | ApiError> {
    try {
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) return { error: 'User not authenticated with PromptCircle' };

      const cookies = await chrome.cookies.getAll({ domain: 'promptcircle.ai' });
      const csrfCookie = cookies.find((c) => c.name === 'csrftoken');
      if (!csrfCookie) return { error: 'No CSRF token found. Please login to PromptCircle first.' };

      const response = await fetch(`${this.apiURL}/saved-responses/${id}/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfCookie.value,
        },
        credentials: 'include',
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      return { success: true };
    } catch (error) {
      console.error('Error deleting saved response:', error);
      return { error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  }

  /**
   * Save a new prompt to PromptCircle API
   */
  async savePrompt(promptData: {
    title: string;
    content: string;
    tags: string[];
    folder: string;
    compatible_llms: string[];
    is_favorite: boolean;
  }): Promise<SavedPrompt | ApiError> {
    try {
      // Check authentication first
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        return { error: "User not authenticated with PromptCircle" };
      }

      // Get CSRF token from cookies
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");

      if (!csrfCookie) {
        return {
          error: "No CSRF token found. Please login to PromptCircle first.",
        };
      }

      // Make API request
      const response = await fetch(`${this.apiURL}/saved-prompts/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": csrfCookie.value,
        },
        body: JSON.stringify(promptData),
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      return data as SavedPrompt;
    } catch (error) {
      console.error("Error saving prompt:", error);
      return {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Update an existing prompt via PromptCircle API
   */
  async updatePrompt(
    id: string,
    promptData: Partial<{
      title: string;
      content: string;
      tags: string[];
      folder: string;
      compatible_llms: string[];
      is_favorite: boolean;
    }>
  ): Promise<SavedPrompt | ApiError> {
    try {
      // Check authentication first
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        return { error: "User not authenticated with PromptCircle" };
      }

      // Get CSRF token from cookies
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");

      if (!csrfCookie) {
        return {
          error: "No CSRF token found. Please login to PromptCircle first.",
        };
      }

      // Make API request
      const response = await fetch(`${this.apiURL}/saved-prompts/${id}/`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": csrfCookie.value,
        },
        body: JSON.stringify(promptData),
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      return data as SavedPrompt;
    } catch (error) {
      console.error("Error updating prompt:", error);
      return {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Delete a saved prompt via PromptCircle API
   */
  async deletePrompt(id: string): Promise<{ success: boolean } | ApiError> {
    try {
      // Check authentication first
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        return { error: "User not authenticated with PromptCircle" };
      }

      // Get CSRF token from cookies
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");

      if (!csrfCookie) {
        return {
          error: "No CSRF token found. Please login to PromptCircle first.",
        };
      }

      // Make API request
      const response = await fetch(`${this.apiURL}/saved-prompts/${id}/`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": csrfCookie.value,
        },
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      return { success: true };
    } catch (error) {
      console.error("Error deleting prompt:", error);
      return {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Track usage of a saved prompt via PromptCircle API
   */
  async usePrompt(id: string): Promise<UsePromptResponse | ApiError> {
    try {
      // Check authentication first
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        return { error: "User not authenticated with PromptCircle" };
      }

      // Get CSRF token from cookies
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");

      if (!csrfCookie) {
        return {
          error: "No CSRF token found. Please login to PromptCircle first.",
        };
      }

      // Make API request
      const response = await fetch(`${this.apiURL}/saved-prompts/${id}/use`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": csrfCookie.value,
        },
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      return data as UsePromptResponse;
    } catch (error) {
      console.error("Error using prompt:", error);
      return {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }
}

// Export singleton instance
export const promptCircleApi = new PromptCircleApiService();
